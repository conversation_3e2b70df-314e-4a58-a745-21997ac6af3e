<template>
  <div class="live-chat-wrapper">
    <!-- 只有在非loading状态下才渲染场景内容 -->
    <template v-if="!stateMachine.isLoading.value">
      <!-- 直播场景 -->
      <div
        v-if="
          Chat4SceneUtils.isLivingScene(currentScene) && !showStreamEndedOverlay
        "
        class="live-scene"
      >
        <!-- 直播容器 -->
        <LiveStreamContainer
          ref="liveStreamContainerRef"
          :is-playing-video="isPlayingVideo"
          :background-video="backgroundVideo"
          :background-image="backgroundImage"
          :default-image="defaultBackgroundImage"
          :animated-images="chatResourcesStore.animatedImages"
          :content-ready="contentReady"
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :viewer-count="viewerCount"
          :is-live="true"
          :use-seamless-video="true"
          :is-muted="audioManager.isMuted"
          :video-group="chatResourcesStore.videoGroupQueue"
          @container-click="handleContainerClick"
          @back-click="handleBackClick"
          @audio-enabled="handleAudioEnabled"
          @play-interaction="handlePlayInteraction"
          @user-interaction="handleUserInteraction"
        >
          <!-- 实时评论流 -->
          <template #comment-flow>
            <CommentFlow
              :comments="liveComments"
              :is-visible="!chatUIStore.overlay"
            />
          </template>

          <!-- 心形粒子效果 -->
          <template #heart-particles>
            <HeartParticles :click-trigger="heartClickCount" />
          </template>

          <!-- 钻石积分显示 -->
          <template #coin-display>
            <CreditDisplay
              :amount="userStore.userInfo?.coins || 0"
              :show-add-button="true"
              @add="() => handleAddCredit('credit')"
            />
          </template>

          <!-- 倒计时器 -->
          <template #timer>
            <Timer
              ref="timerRef"
              :is-countdown="true"
              :max-duration="65"
              @time-up="handleTimeUp"
            />
          </template>

          <!-- Stage 好感度系统 -->
          <template #stage>
            <Stage
              :favorability-state="chatEventsStore.favorabilityState"
              :compact="true"
              @click="handleStageClick"
            />
          </template>

          <!-- 底部控制栏 -->
          <template #stream-controls>
            <StreamControls
              :is-muted="audioManager.isMuted"
              @send-comment="handleSendComment"
              @open-gift-modal="handleOpenGiftModal"
              @heart-click="handleHeartClick"
              @toggle-mute="audioManager.toggleMute"
            />
          </template>

          <!-- 覆盖层内容 -->
          <template #overlay>
            <StreamOverlay
              v-if="chatUIStore.overlay"
              :overlay="chatUIStore.overlay"
              :say-hi-text="sayHiText"
              @overlay-button-click="() => handleOverlayButton()"
            />
          </template>

          <!-- 结束内容 -->
          <template #ending>
            <EndingContent
              v-if="chatUIStore.isEnding && chatUIStore.endings"
              :endings="chatUIStore.endings"
              @restart="handleRestart"
            />
          </template>

          <!-- 跳转到聊天室按钮 - 接受好友请求后或从聊天室返回后显示 -->
          <template
            #chat-button
            v-if="
              friendRequestAccepted ||
              hasReturnedFromChat ||
              chatEventsStore.favorabilityState.currentHeartValue > 10
            "
          >
            <div class="chat-button-container">
              <button
                class="chat-button"
                @click="handleGoToChat"
                :disabled="!canGoToChat"
              >
                <div class="chat-icon">💬</div>
                <span class="chat-text">Chat</span>
              </button>
            </div>
          </template>
        </LiveStreamContainer>

        <!-- 好友请求消息 -->
        <FriendRequestMessage
          :visible="
            showFriendRequestMessage &&
            Chat4SceneUtils.isLivingScene(currentScene)
          "
          :actor-name="storyStore.currentActor?.name || 'Tsunade'"
          :actor-avatar="storyStore.currentActor?.avatar_url || ''"
          @accept="handleFriendRequestAccept"
        />
      </div>

      <!-- 聊天室场景 -->
      <div
        v-else-if="Chat4SceneUtils.isPhoneScene(currentScene)"
        class="chat-scene"
      >
        <ChatRoomContainer
          ref="chatRoomContainerRef"
          :character-image="storyStore.currentActor?.avatar_url"
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :background-video="backgroundVideo"
          :background-image="backgroundImage"
          :default-image="defaultBackgroundImage"
          :animated-images="chatResourcesStore.animatedImages"
          :current-scene="currentScene"
          @message-sent="handleChatRoomMessage"
          @input-focus="handleChatInputFocus"
          @input-blur="handleChatInputBlur"
          @scene-event="handleChatRoomSceneEvent"
          @nav-click="handleNavClick"
          @back-to-live="handleBackToLive"
          @back-click="handleBackClick"
        >
          <!-- 钻石积分显示 -->
          <template #coin-display>
            <CreditDisplay
              :amount="userStore.userInfo?.coins || 0"
              :show-add-button="true"
              @add="() => handleAddCredit('credit')"
            />
          </template>

          <!-- 聊天界面 -->
          <template #chat-interface>
            <ChatInterface
              :messages="displayMessages"
              :actor-name="storyStore.currentActor?.name || 'Tsunade'"
              :actor-avatar="storyStore.currentActor?.avatar_url"
            />
          </template>

          <!-- Stage 好感度系统 -->
          <template #stage>
            <Stage
              :favorability-state="chatEventsStore.favorabilityState"
              :compact="true"
              @click="handleStageClick"
            />
          </template>
        </ChatRoomContainer>
      </div>

      <!-- 视频通话场景 -->
      <div
        v-else-if="Chat4SceneUtils.isVideoCallScene(currentScene)"
        class="video-call-scene"
      >
        <VideoCallContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          :is-muted="audioManager.isMuted"
          :latest-message="''"
          @back-click="handleBackClick"
          @hangup-click="handleHangupVideo"
          @toggle-mute="audioManager.toggleMute"
          @send-message="handleVideoCallMessage"
        />
      </div>

      <!-- 监控场景 -->
      <div
        v-else-if="Chat4SceneUtils.isMonitorScene(currentScene)"
        class="monitor-scene"
      >
        <MonitorContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          @go-to-living-room="handleGoToLivingRoom"
        />
      </div>

      <!-- 地图场景 -->
      <div
        v-else-if="Chat4SceneUtils.isMapScene(currentScene)"
        class="map-scene"
      >
        <MapContainer
          :favorability-state="chatEventsStore.favorabilityState"
          @location-click="handleMapLocationClick"
          @back-click="handleBackFromMap"
        />
      </div>

      <!-- 日记场景 -->
      <div
        v-else-if="Chat4SceneUtils.isDiaryScene(currentScene)"
        class="diary-scene"
      >
        <DiaryContainer
          :character-name="storyStore.currentActor?.name || 'Kurapika'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          @go-back="handleGoBackFromDiary"
        />
      </div>

      <!-- 大头贴场景 -->
      <div
        v-else-if="Chat4SceneUtils.isPhotoBoothScene(currentScene)"
        class="photo-booth-scene"
      >
        <PhotoBoothContainer
          :character-name="storyStore.currentActor?.name || 'Character'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          @back-click="handleBackFromPhotoBooth"
        />
      </div>

      <!-- 约会场景 -->
      <div
        v-else-if="Chat4SceneUtils.isMeetupScene(currentScene)"
        class="meetup-scene"
      >
        <MeetupContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          :messages="displayMessages"
          :currentScene="currentScene"
          @back-click="handleBackFromMeetup"
          @send-message="handleMeetupMessage"
          @open-gift-modal="handleOpenGiftModal"
          @stage-click="handleStageClick"
        >
          <!-- 钻石积分显示 -->
          <template #coin-display>
            <CreditDisplay
              :amount="userStore.userInfo?.coins || 0"
              :show-add-button="true"
              @add="() => handleAddCredit('credit')"
            />
          </template>
        </MeetupContainer>
      </div>

      <!-- 舞蹈场景 -->
      <div
        v-else-if="Chat4SceneUtils.isDancingScene(currentScene)"
        class="dancing-scene"
      >
        <DancingContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          :current-scene="currentScene"
          @back-click="handleBackFromDancing"
          @message-sent="handleDancingMessage"
          @scene-event="handleDancingSceneEvent"
        >
          <template #stage>
            <Stage
              :favorability-state="chatEventsStore.favorabilityState"
              :compact="true"
              @click="handleStageClick"
            />
          </template>
          <template #credit-display>
            <CreditDisplay
              :amount="userStore.userInfo?.coins || 0"
              :show-add-button="true"
              @add="() => handleAddCredit('credit')"
            />
          </template>
        </DancingContainer>
      </div>

      <!-- 演唱会场景 -->
      <div
        v-else-if="Chat4SceneUtils.isConcertScene(currentScene)"
        class="concert-scene"
      >
        <ConcertContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          :background-image="backgroundImage"
          :background-video="backgroundVideo"
          :current-scene="currentScene"
          @back-click="handleBackFromConcert"
          @message-sent="handleConcertMessage"
          @scene-event="handleConcertSceneEvent"
          @danmaku-sent="handleConcertDanmaku"
        >
          <template #back-button>
            <div class="back-button" @click="handleBackFromConcert">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M15 18L9 12L15 6"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </template>

          <template #stage>
            <Stage
              :favorability-state="chatEventsStore.favorabilityState"
              :compact="true"
              @click="handleStageClick"
            />
          </template>

          <template #credit-display>
            <CreditDisplay
              :amount="userStore.userInfo?.coins || 0"
              :show-add-button="true"
              @add="() => handleAddCredit('credit')"
            />
          </template>

          <!-- 心形粒子效果 -->
          <template #heart-particles>
            <HeartParticles :click-trigger="heartClickCount" />
          </template>

          <template #bottom-actions>
            <div class="controls-group">
              <!-- 礼物按钮 -->
              <button
                class="control-button gift-button"
                @click="handleOpenGiftModal"
              >
                <GiftIcon class="button-icon" />
              </button>

              <!-- 心形点赞按钮 -->
              <button
                class="control-button heart-button"
                @click="handleHeartClick"
                :class="{ clicking: isHeartClicking }"
              >
                <HeartIcon class="button-icon" />
              </button>
            </div>
          </template>
        </ConcertContainer>
      </div>

      <!-- 朋友圈场景 -->
      <div
        v-else-if="Chat4SceneUtils.isMomentScene(currentScene)"
        class="moment-scene"
      >
        <MomentContainer
          :character-name="storyStore.currentActor?.name || 'Tsunade'"
          :character-avatar="storyStore.currentActor?.avatar_url"
          @back-click="handleBackFromMoment"
        />
      </div>

      <!-- 模态框 -->
      <LeaveConfirmModal
        v-model:visible="showLeaveConfirmModal"
        @confirm="handleLeaveConfirm"
        @cancel="handleLeaveCancel"
      />
      <AuthDrawer
        :isInLandingPage="!!props.characterId"
        v-model:visible="showAuthDrawer"
        @login="handleLoginSuccess"
        @register="handleRegisterSuccess"
      />
      <ShareModal v-model:visible="showShareModal" />
      <EndingShareModal />

      <!-- 付费场景提醒对话框 -->
      <ConfirmDialog
        v-model:visible="chatEventsStore.showPaidSceneDialog"
        title="Spicy Level"
        :title-style="{ color: '#FFF' }"
        :content-style="{
          color: '#CA93F2',
          fontWeight: '600',
          textAlign: 'left',
        }"
        confirm-text="I Understand"
        :show-cancel="false"
        :show-icon="false"
        :close-on-click-overlay="false"
        @confirm="chatEventsStore.showPaidSceneDialog = false"
      >
        <template #content>
          <p>You have entered the spicy level. Enjoy your spicy time.</p>
          <p>
            Note: This level offers 5 minutes of free time, and the subsequent
            content is priced at 5 diamonds per 5 minutes.
          </p>
        </template>
      </ConfirmDialog>

      <!-- 好友请求接受弹窗 -->
      <FriendRequestModal
        v-model:visible="showFriendRequestModal"
        @keep-watching="handleKeepWatching"
        @start-chatting="handleStartChatting"
      />

      <!-- 礼物选择抽屉 -->
      <GiftDrawer
        v-model:visible="showGiftModal"
        @gift-sent="handleGiftSentWithAnimation"
      />

      <!-- 充值弹窗 -->
      <!-- <RechargeModal /> -->

      <!-- 好感度抽屉 -->
      <FavorabilityDrawer
        v-model:visible="showFavorabilityDrawer"
        :favorability-state="chatEventsStore.favorabilityState"
        @close="handleFavorabilityDrawerClose"
        @payment="handleFavorabilityPayment"
      />

      <!-- 付费确认弹窗 -->
      <PaymentConfirmModal
        ref="paymentConfirmModalRef"
        v-model:visible="chat4Store.unlockState.showPaymentConfirmModal"
        :coins="chat4Store.unlockState.paymentCoins"
        @confirm="handlePaymentConfirm"
        @cancel="chat4Store.closePaymentConfirmModal"
      />
    </template>

    <!-- 直播结束覆盖层 -->
    <StreamEndedOverlay
      :visible="showStreamEndedOverlay"
      :actor-name="storyStore.currentActor?.name || 'Tsunade'"
      :actor-avatar="storyStore.currentActor?.avatar_url || ''"
      :background-image="storyStore.currentActor?.preview_url || ''"
      @back-click="handleStreamEndedBack"
      @explore-others="handleExploreOthers"
      @start-chatting="handleStreamEndedStartChatting"
    />

    <!-- Chat4 Loading 组件 - 只在非微前端环境显示 -->
    <Chat4Loading
      v-if="shouldShowInternalLoading"
      :visible="stateMachine.isLoading.value"
      :character-avatar="storyStore.currentActor?.avatar_url"
      :character-name="storyStore.currentActor?.name || 'Character'"
      :loading-type="stateMachine.loadingType.value"
      :custom-title="stateMachine.loadingTitle.value"
      :custom-subtitle="stateMachine.loadingSubtitle.value"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  watch,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
  ref,
} from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { setDynamicSEO } from '@/router/seo-guard'
import { useGameInitialization } from '../chat2/composables/useGameInitialization'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { useVideoCanvas } from '@/mobile/composables/useVideoCanvas'
import { Chat4SceneUtils } from '@/types/chat4-scene'

import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import {
  isInMicroFrontend,
  reportLoadingProgress,
  sendContentReadyMessage,
} from '@/utils/iframeNavigation'

// Chat4 Composables
import { useChat4State } from './composables/useChat4State'
import { useChat4Messages } from './composables/useChat4Messages'
import { useChat4Events } from './composables/useChat4Events'

// Chat4 Store
import { useChat4Store } from '@/store/chat4'

// Chat4专用组件导入 - 暂时使用占位符组件
import LiveStreamContainer from './components/LiveStreamContainer/index.vue'
import CommentFlow from './components/CommentFlow/index.vue'
import HeartParticles from './components/HeartParticles/index.vue'
import MapContainer from './components/MapContainer/index.vue'

// 注意：TTS 管理器现在由 Chat4 Store 统一管理

import Timer from './components/Timer/index.vue'
import StreamControls from './components/StreamControls/index.vue'
import StreamOverlay from './components/StreamOverlay/index.vue'
import EndingContent from './components/EndingContent/index.vue'
import FriendRequestModal from './components/FriendRequestModal/index.vue'
import FriendRequestMessage from './components/FriendRequestMessage/index.vue'
import StreamEndedOverlay from './components/StreamEndedOverlay/index.vue'
import ChatRoomContainer from './components/ChatRoomContainer/index.vue'
import ChatInterface from './components/ChatInterface/index.vue'
import Chat4Loading from './components/Chat4Loading/index.vue'
import VideoCallContainer from './components/VideoCallContainer/index.vue'
import MonitorContainer from './components/MonitorContainer/index.vue'
import MeetupContainer from './components/MeetupContainer/index.vue'
import DancingContainer from './components/DancingContainer/index.vue'
import ConcertContainer from './components/ConcertContainer/index.vue'
import MomentContainer from './components/MomentContainer/index.vue'
import DiaryContainer from './components/DiaryContainer/index.vue'
import { PhotoBoothContainer } from './components/PhotoBooth'

// 图标导入（用于Concert场景的控制按钮）
import GiftIcon from '@/assets/icon/gift2-icon.svg'
import HeartIcon from '@/assets/icon/heart-icon.svg'

import Stage from './components/Stage/index.vue'
import GiftDrawer from './components/GiftDrawer/index.vue'
import FavorabilityDrawer from './components/FavorabilityDrawer/index.vue'
import PaymentConfirmModal from './components/PaymentConfirmModal/index.vue'

// 保留的组件导入
import ShareModal from '../chat2/components/ShareModal/index.vue'
import LeaveConfirmModal from '@/mobile/components/LeaveConfirmModal.vue'
import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
import EndingShareModal from '../chat2/components/EndingShareModal/index.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import CreditDisplay from '@/shared/components/CreditDisplay.vue'
// import RechargeModal from '@/mobile/components/RechargeModal.vue'

// Props
const props = defineProps<{
  characterId?: string
  storyId?: string
}>()

// Timer 组件引用
const timerRef = ref<InstanceType<typeof Timer>>()

// LiveStreamContainer 组件引用
const liveStreamContainerRef = ref<InstanceType<typeof LiveStreamContainer>>()

// ChatRoomContainer 组件引用
const chatRoomContainerRef = ref<InstanceType<typeof ChatRoomContainer>>()

// Chat4 State Management
const state = useChat4State()

// 条件显示内部loading - 现在在所有环境中都显示
const shouldShowInternalLoading = computed(() => {
  // 在所有环境中都显示Chat4Loading，确保视觉连续性
  // RemoteChatLoader和Chat4Loading样式一致，用户感觉是同一个loading的延续
  return true
})

// 组件引用
const paymentConfirmModalRef = ref()
const {
  route,
  chatEventsStore,
  chatUIStore,
  chatResourcesStore,
  chatMessagesStore,
  storyStore,
  userStore,
  skillStore,
  stateMachine,
  currentScene,
  contentReady,
  showAuthDrawer,
  showShareModal,
  showLeaveConfirmModal,
  showFriendRequestModal,
  showFriendRequestMessage,
  friendRequestAccepted,
  showStreamEndedOverlay,
  showGiftModal,
  showFavorabilityDrawer,
  heartClickCount,
  viewerCount,
  sayHiText,
  nextCallback,
  startTime,
  handleChat4VisibilityChange,
  clearUIPopupStates,
} = state

// Chat4 Store Management
const chat4Store = useChat4Store()

// 同步状态机的场景到 store
watch(
  currentScene,
  (newScene) => {
    chat4Store.setCurrentScene(newScene)
  },
  { immediate: true },
)

// 监听心值变化，更新背景模糊状态
watch(
  () => chatEventsStore.favorabilityState.currentHeartValue,
  () => {
    // 当心值变化时，重新评估模糊状态
    chatResourcesStore.updateBlurStateBasedOnHeartValue()
  },
  { immediate: false },
)

// Chat4 Messages Management
const messages = useChat4Messages()
const {
  liveComments,
  displayMessages,
  addJoinRoomMessage,
  addGiftMessage,
  clearLocalGiftMessages,
  clearDanmakuTimers,
  clearGlobalDebounceTimer,
  handleChat4DanmakuEvent,
} = messages

// 跟踪是否从聊天室返回过（用于显示聊天室按钮）
const hasReturnedFromChat = ref(false)

// Concert场景的心形按钮点击状态
const isHeartClicking = ref(false)

// 包装心形点击函数，添加动画效果
const handleHeartClick = () => {
  // 触发点击动画
  isHeartClicking.value = true
  setTimeout(() => {
    isHeartClicking.value = false
  }, 200)

  // 调用原始的心形点击处理
  originalHandleHeartClick()
}

// 服务器场景变化事件处理器
const handleServerSceneChange = (event: CustomEvent) => {
  const { sceneId } = event.detail
  console.log('Chat4: Received server scene change event:', sceneId)
  stateMachine.handleServerSceneChange(sceneId)
}

// 监听场景切换，清理旧场景状态
watch(
  currentScene,
  (newScene, oldScene) => {
    // 清理旧场景状态
    if (oldScene && newScene !== oldScene) {
      // 通过 Chat4 Store 清理旧场景的状态
      chat4Store.clearSceneSpecificState(oldScene)

      // 只在离开Living场景时清理弹幕计时器和UI弹窗状态
      if (Chat4SceneUtils.isLivingScene(oldScene)) {
        clearDanmakuTimers()
        // 离开直播场景时清理好友请求相关的UI状态
        clearUIPopupStates()
      }

      // 检测从聊天室返回到直播间的操作
      if (
        Chat4SceneUtils.isPhoneScene(oldScene) &&
        Chat4SceneUtils.isLivingScene(newScene)
      ) {
        hasReturnedFromChat.value = true
      }
    }
  },
  { immediate: false },
)

// 监听场景切换，控制倒计时
watch(
  currentScene,
  (newScene) => {
    if (!timerRef.value) {
      return
    }

    // 只有在直播场景时才运行倒计时
    if (Chat4SceneUtils.isLivingScene(newScene)) {
      // 切换到直播场景，开始倒计时
      timerRef.value.startCountdown()
      // 切换到直播场景时添加进入房间提醒
      addJoinRoomMessage()
    } else {
      // 切换到其他场景，停止倒计时
      timerRef.value.stopCountdown()
    }
  },
  { immediate: false },
)

// Composables
const { startGame, handleVisibilityChange, cleanup } = useGameInitialization()
const audioManager = useAudioManager()
useVideoCanvas() // For video canvas functionality

// Chat4 Events Management
const events = useChat4Events(props, state, {
  paymentConfirmModalRef,
  chat4Store,
})
const {
  handleBackHome,
  handleContainerClick,
  handleOverlayButton,
  handleRestart: originalHandleRestart,
  handleSendComment,
  handleOpenGiftModal,
  handleGiftSent,
  handleHeartClick: originalHandleHeartClick,
  handleTimeUp,
  handleGoToChat,
  handleBackToLive,
  handleHangupVideo,
  handleVideoCallMessage,
  handleGoToLivingRoom,
  handleBackFromMap,
  handleMapLocationClick,
  handleBackFromMeetup,
  handleMeetupMessage,
  handleBackFromDancing,
  handleDancingMessage,
  handleDancingSceneEvent,
  handleBackFromConcert,
  handleBackFromMoment,
  handleGoBackFromDiary,
  handleBackFromPhotoBooth,
  handlePhotoBoothAlbum,
  handlePhotoBoothCapture,
  handleConcertMessage,
  handleConcertSceneEvent,
  handleConcertDanmaku,
  handleChatRoomMessage,
  handleChatInputFocus,
  handleChatInputBlur,
  handleChatRoomSceneEvent,
  handleNavClick,
  handleLoginSuccess,
  handleRegisterSuccess,
  handleBackClick,
  handleLeaveConfirm,
  handleLeaveCancel,
  handleFriendRequestAccept,
  handleKeepWatching,
  handleStartChatting,
  handleAddCredit,
  handleStreamEndedBack,
  handleExploreOthers,
  handleStreamEndedStartChatting,
  handleStageClick,
  handleFavorabilityDrawerClose,
  handleFavorabilityPayment,
  handlePaymentConfirm,
  handleAudioEnabled,
  handlePlayInteraction,
  handleUserInteraction,
} = events

// 礼物发送处理函数，添加到聊天列表
const handleGiftSentWithAnimation = (gift: any, quantity: number = 1) => {
  // 调用原有的handleGiftSent逻辑（增加心形点击计数），传递quantity参数
  handleGiftSent(gift, quantity)

  // 将礼物添加到聊天消息列表中
  addGiftMessage(gift, quantity, userStore.userInfo?.avatar_url)

  // 触发礼物特效动画
  if (Chat4SceneUtils.isLivingScene(currentScene.value)) {
    // 直播场景使用LiveStreamContainer的动画
    if (liveStreamContainerRef.value) {
      liveStreamContainerRef.value.playGiftAnimation(gift, 'You', quantity)
    }
  } else if (Chat4SceneUtils.isPhoneScene(currentScene.value)) {
    // 聊天室场景使用ChatRoomContainer的动画
    if (chatRoomContainerRef.value) {
      chatRoomContainerRef.value.playGiftAnimation(gift, 'You', quantity)
    }
  }
}

// 包装重新开始处理函数，清除解锁提示历史
const handleRestart = async () => {
  // 清除聊天室容器的解锁提示历史
  if (chatRoomContainerRef.value) {
    chatRoomContainerRef.value.clearUnlockToastHistory()
  }

  // 调用原始的重新开始逻辑
  await originalHandleRestart()
}

// Computed properties
const isPlayingVideo = computed(() => chatResourcesStore.isPlayingVideo)
const backgroundVideo = computed(() => {
  const videoUrl = chatResourcesStore.backgroundVideo
  console.log('📺 [chat4/index] backgroundVideo computed变化:', {
    videoUrl: videoUrl?.substring(0, 60) + '...',
    currentScene: currentScene.value,
    isVideoGroupPlaying: chatResourcesStore.isPlayingVideoGroup,
    timestamp: Date.now(),
  })
  return videoUrl
})
const backgroundImage = computed(() => chatResourcesStore.backgroundImage)
const defaultBackgroundImage = computed(
  () => storyStore.currentActor?.preview_url,
)
const canGoToChat = computed(() => {
  return (
    !!storyStore.currentStory?.id &&
    !!storyStore.currentActor?.id &&
    Chat4SceneUtils.isLivingScene(currentScene.value) &&
    (friendRequestAccepted.value || hasReturnedFromChat.value) // 接受好友请求后或从聊天室返回后都能进入聊天室
  )
})

// 监听场景切换，上报相关事件
watch(currentScene, (newScene, oldScene) => {
  if (newScene && newScene !== oldScene) {
    // 确定场景类型
    let sceneType = 'unknown'
    if (Chat4SceneUtils.isLivingScene(newScene)) {
      sceneType = 'livestream'
    } else if (Chat4SceneUtils.isPhoneScene(newScene)) {
      sceneType = 'mobile_chat'
    } else if (Chat4SceneUtils.isVideoCallScene(newScene)) {
      sceneType = 'facetime'
    } else if (Chat4SceneUtils.isMonitorScene(newScene)) {
      sceneType = 'monitor'
    } else if (Chat4SceneUtils.isMeetupScene(newScene)) {
      sceneType = 'meetup'
    } else if (
      Chat4SceneUtils.isMomentScene &&
      Chat4SceneUtils.isMomentScene(newScene)
    ) {
      sceneType = 'moments'
    } else if (
      Chat4SceneUtils.isDiaryScene &&
      Chat4SceneUtils.isDiaryScene(newScene)
    ) {
      sceneType = 'diary'
    }

    // 统一上报场景进入事件
    reportEvent(ReportEvent.SceneAction, {
      userId: userStore.userInfo?.uuid,
      story_id: route.params.storyId,
      character_id: route.params.actorId,
      action_type: 'enter',
      scene_type: sceneType,
      current_scene: newScene,
      previous_scene: oldScene,
    })

    // 场景切换时清理本地礼物消息，避免礼物在不同场景间显示
    clearLocalGiftMessages()
  }
})

onMounted(async () => {
  const { proxy } = getCurrentInstance()
  // @ts-ignore
  proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)

  const actorId = (route.params.actorId as string) || props.characterId
  const storyId = (route.params.storyId as string) || props.storyId

  // 正常模式：使用真实游戏逻辑
  if (actorId && storyId) {
    await startGame(actorId, storyId)
    // Chat4特有：游戏初始化完成后，通知状态机隐藏加载界面
    // 这对于重新开始游戏特别重要，因为服务器可能不会发送场景变化事件
    // 延迟一点时间，给服务器场景变化事件一个机会
    // 如果服务器在3秒内发送了场景变化事件，那么会被handleServerSceneChange处理
    // 如果没有，这个备用机制会确保加载界面被隐藏
    setTimeout(() => {
      if (
        stateMachine.isLoading.value &&
        stateMachine.loadingType.value === 'connection'
      ) {
        console.log(
          'No server scene change received, completing initialization manually',
        )
        stateMachine.completeInitialization()
        sendContentReadyMessage
      }

      // 在微前端环境中报告Chat4初始化完成进度
      if (isInMicroFrontend()) {
        reportLoadingProgress('chat4-init', 90)
      }
    }, 3000)
  }

  // 设置聊天页面SEO
  if (storyStore.currentStory) {
    const actorName = storyStore.currentActor?.name
    setDynamicSEO('Chat4', {
      story: storyStore.currentStory,
      actorName,
    })
  }
  document.addEventListener('visibilitychange', handleVisibilityChange)
  document.addEventListener('visibilitychange', handleChat4VisibilityChange)

  // 记录开始时间用于统计聊天时长
  startTime.value = Date.now()

  // 根据初始场景设置倒计时状态 todo: 待废弃
  if (timerRef.value) {
    if (!currentScene.value) {
      // 没有场景，停止计时器
      console.log(
        'No initial scene yet, waiting for server response - timer will start when scene is received',
      )
      timerRef.value.stopCountdown()
      return
    }

    const isLive = Chat4SceneUtils.isLivingScene(currentScene.value)
    if (isLive) {
      console.log('Initial scene is live, starting countdown')
      timerRef.value.startCountdown()
      addJoinRoomMessage()
    } else {
      console.log('Initial scene is not live, stopping countdown')
      timerRef.value.stopCountdown()
    }
  }

  // 注册弹幕事件监听器（在主页面注册，避免场景切换时丢失）
  window.addEventListener('chat4DanmakuEvent', handleChat4DanmakuEvent as any)

  // 注册服务器场景变化事件监听器
  window.addEventListener(
    'chat4ServerSceneChange',
    handleServerSceneChange as any,
  )

  contentReady.value = true
})

onBeforeUnmount(() => {
  cleanup()
  skillStore.selectedSkills = []
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  document.removeEventListener('visibilitychange', handleChat4VisibilityChange)

  // 移除弹幕事件监听器
  window.removeEventListener(
    'chat4DanmakuEvent',
    handleChat4DanmakuEvent as any,
  )

  // 移除服务器场景变化事件监听器
  window.removeEventListener(
    'chat4ServerSceneChange',
    handleServerSceneChange as any,
  )

  // 清理全局防抖计时器
  clearGlobalDebounceTimer()

  // 清理所有弹幕计时器
  clearDanmakuTimers()

  // 停止倒计时
  if (timerRef.value) {
    timerRef.value.stopCountdown()
  }

  // 清理UI弹窗状态
  clearUIPopupStates()

  // 清理 Chat4 Store 状态
  chat4Store.resetAllState()
})

onBeforeRouteLeave((to, _from, next) => {
  console.log('Chat4 route leave to:', to.path)

  // 停止倒计时
  if (timerRef.value) {
    timerRef.value.stopCountdown()
  }

  // 清理UI弹窗状态
  clearUIPopupStates()

  // 如果是前往登录页面、发生错误、需要充值或已经通过钻石用完模态框确认离开，则直接离开
  if (
    to.path === '/user/login' ||
    chatUIStore.isError ||
    storyStore.isNeedRecharge ||
    chatEventsStore.isConfirmedLeave
  ) {
    console.log(
      'Direct leave allowed, isConfirmedLeave:',
      chatEventsStore.isConfirmedLeave,
    )
    // 如果是通过钻石用完模态框确认离开，重置标记
    if (chatEventsStore.isConfirmedLeave) {
      chatEventsStore.isConfirmedLeave = false
    }
    next()
    return
  }
  nextCallback.set(next)
  console.log('Show leave confirm modal')
  showLeaveConfirmModal.value = true
})

defineOptions({
  name: 'Chat4View',
})
</script>

<style lang="less" scoped>
/* 聊天场景样式 */
.chat-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 聊天按钮样式 */
.chat-button-container {
  .chat-button {
    width: 60px;
    height: 60px;
    background: rgba(76, 60, 89, 0.9);
    border: 2px solid rgba(218, 255, 150, 0.3);
    border-radius: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    &:hover:not(:disabled) {
      background: rgba(76, 60, 89, 1);
      border-color: rgba(218, 255, 150, 0.6);
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .chat-icon {
      font-size: 24px;
      margin-bottom: 2px;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    }

    .chat-text {
      font-family: 'Work Sans', sans-serif;
      font-size: 10px;
      font-weight: 600;
      color: #ffffff;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    @media (min-width: 768px) {
      width: 70px;
      height: 70px;
      border-radius: 35px;

      .chat-icon {
        font-size: 28px;
      }

      .chat-text {
        font-size: 11px;
      }
    }
  }
}

.live-chat-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
  background: linear-gradient(180deg, #1f0038 0%, #000 100%);
}

/* Figma聊天室场景样式 */
.chat-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 视频通话场景样式 */
.video-call-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 监控场景样式 */
.monitor-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 约会场景样式 */
.meetup-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 舞蹈场景样式 */
.dancing-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 演唱会场景样式 */
.concert-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

/* 朋友圈场景样式 */
.moment-scene {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

.concert-actions {
  display: flex;
  gap: 16px;
}

.action-button {
  width: 44px;
  height: 44px;
  background: rgba(46, 23, 65, 0.65);
  border: none;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(7px);

  .button-icon {
    font-size: 24px;
  }

  &:hover {
    transform: scale(1.05);
    background: rgba(46, 23, 65, 0.8);
  }

  &:active {
    transform: scale(0.95);
  }
}

.back-button {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
}

.figma-back-btn {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .back-icon {
    font-family: 'SF Pro', sans-serif;
    font-weight: 400;
    font-size: 25px;
    line-height: 1.193;
    text-align: center;
    color: #ffffff;
  }
}

/* Concert场景控制按钮样式 - 参考StreamControls */
.controls-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
  }
}

.gift-button {
  background: #4c3c59;
  box-shadow: 0px 0px 10px rgba(218, 255, 150, 0.15);

  .button-icon {
    fill: #daff96;
    width: 20px;
    height: 20px;
  }

  &:hover {
    box-shadow: 0px 0px 15px rgba(218, 255, 150, 0.25);
  }
}

.heart-button {
  background: #4c3c59;
  box-shadow: 0px 0px 10px rgba(218, 255, 150, 0.15);
  position: relative;
  overflow: visible;

  .button-icon {
    fill: #ff004d;
  }

  &:hover {
    box-shadow: 0px 0px 15px rgba(218, 255, 150, 0.25);
  }

  &.clicking {
    animation: heartPulse 0.2s ease;
  }

  &:active .button-icon {
    transform: scale(1.2);
  }
}

@keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// PhotoBooth场景样式
.photo-booth-scene {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>

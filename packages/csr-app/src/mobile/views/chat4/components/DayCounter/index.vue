<template>
  <div class="day-counter" :class="`theme-${theme}`">
    <!-- 左侧：天数显示区域 -->
    <div class="days-display">
      <span class="get-along-text">Get Along:</span>
      <span class="days-number">{{ displayDays }}</span>
      <span class="days-unit">Days</span>
    </div>

    <!-- 右侧：Next Day 按钮 -->
    <button
      class="next-day-button"
      :disabled="isProcessing"
      @click="handleNextDay"
    >
      <span v-if="!isProcessing" class="button-text">Next Day</span>
      <span v-else class="button-text">Loading...</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useStoryStore } from '@/store/story'
import { useChat4Store } from '@/store/chat4'
import { useTimeSystem } from '@/composables/useTimeSystem'
import { useChat4StateMachine } from '@/composables/useChat4StateMachine'
import { useChat4Toast } from '../../composables/useChat4Toast'

interface Props {
  /** 故事ID */
  storyId: string
  /** 角色ID */
  actorId: string
  /** 主题样式 */
  theme?: 'default' | 'map' | 'phone'
}

interface Emits {
  /** Next Day 按钮点击事件 */
  (e: 'next-day'): void
  /** 天数重置完成事件 */
  (e: 'day-reset', newDay: number): void
  /** 重置失败事件 */
  (e: 'reset-error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'default',
})

const emit = defineEmits<Emits>()

// ========== Store 引用 ==========
const storyStore = useStoryStore()
const chat4Store = useChat4Store()

// ========== 状态机和Toast ==========
const stateMachine = useChat4StateMachine()
const { showUnlockToast } = useChat4Toast()

// ========== 时间系统集成 ==========
const { currentDay, isLoading, refreshTimeData, resetDays } = useTimeSystem(
  props.storyId,
  props.actorId,
)

// ========== 响应式状态 ==========

/** 是否正在处理 Next Day 请求 */
const isProcessing = ref(false)

/** 显示的天数（带动画效果） */
const displayDays = ref(0)

/** 动画计数器 */
let animationFrame: number | null = null

// ========== 计算属性 ==========

// ========== 方法 ==========

/**
 * 数字动画到指定天数
 */
const animateToDay = (targetDay: number): void => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }

  const startDay = displayDays.value
  const distance = targetDay - startDay
  const duration = Math.min(Math.abs(distance) * 50, 1000) // 最多1秒动画
  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用缓动函数
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3)
    const easedProgress = easeOutCubic(progress)

    displayDays.value = Math.round(startDay + distance * easedProgress)

    if (progress < 1) {
      animationFrame = requestAnimationFrame(animate)
    }
  }

  animate()
}

// ========== 观察器 ==========

/** 监听天数变化，触发动画 */
watch(
  currentDay,
  (newDay) => {
    animateToDay(newDay)
  },
  { immediate: true },
)

/**
 * 处理 Next Day 点击
 */
const handleNextDay = async (): Promise<void> => {
  if (isProcessing.value) return

  emit('next-day')

  isProcessing.value = true

  try {
    console.log('[DayCounter] Next Day clicked, calling reset API')

    // 调用重置天数接口
    const resetResult = await resetDays()

    if (resetResult.success) {
      const newDay = resetResult.data?.days || 1
      console.log(`[DayCounter] Day reset successful, new day: ${newDay}`)
      emit('day-reset', newDay)

      // 显示成功反馈
      showSuccessEffect()

      // 处理场景跳转和toast提示
      await handleDayResetComplete(newDay)
    } else {
      throw new Error(resetResult.error || '重置失败')
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '网络错误'
    console.error('[DayCounter] Next Day 操作失败:', error)
    emit('reset-error', errorMessage)

    // 显示错误反馈
    showErrorEffect()
  } finally {
    isProcessing.value = false
  }
}

/**
 * 处理天数重置完成后的场景跳转和提示
 */
const handleDayResetComplete = async (newDay: number): Promise<void> => {
  try {
    console.log(`[DayCounter] Handling day reset complete, new day: ${newDay}`)

    // 等待一小段时间让用户看到天数变化
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 显示场景切换loading
    chat4Store.setSceneLoading(true, 'scene-transition')

    // 跳转到手机场景
    await stateMachine.goToChat()

    // 等待场景切换完成
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 隐藏loading
    chat4Store.setSceneLoading(false)

    // 显示天数提示toast
    const message =
      newDay === 1
        ? "It's the first day of your relationship"
        : `It's now day ${newDay} of your relationship`

    showUnlockToast(message, 4000) // 显示4秒

    console.log(`[DayCounter] Day reset complete handling finished`)
  } catch (error) {
    console.error('[DayCounter] Day reset complete handling failed:', error)

    // 隐藏loading
    chat4Store.setSceneLoading(false)

    // 显示错误提示
    showUnlockToast('Failed to transition to new day', 3000)
  }
}

/**
 * 显示成功效果
 */
const showSuccessEffect = (): void => {
  console.log('[DayCounter] Day reset successful')
  // 可以添加成功动画或提示
}

/**
 * 显示错误效果
 */
const showErrorEffect = (): void => {
  console.log('[DayCounter] Day reset failed')
  // 可以添加错误动画或提示
}

/**
 * 手动刷新天数
 */
const refresh = async (): Promise<void> => {
  await refreshTimeData()
}

// ========== 公开方法 ==========

defineExpose({
  refresh,
  currentDay,
  isProcessing,
})

// ========== 生命周期 ==========

onMounted(() => {
  // 初始化显示天数
  displayDays.value = currentDay.value
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>

<style lang="less" scoped>
.day-counter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  padding: 8px 0;
  gap: 16px;
}

/* 左侧天数显示区域 */
.days-display {
  display: flex;
  align-items: baseline;
  padding: 8px 12px;
  border-radius: 6px;
  flex: 1;

  .get-along-text {
    color: #fff;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.5);
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: capitalize;
  }

  .days-number {
    font-size: 16px;
    font-weight: 700;
    min-width: 30px;
    text-align: center;
  }

  .days-unit {
    font-size: 14px;
    font-weight: 500;
  }
}

/* 右侧 Next Day 按钮 - 紫色 */
.next-day-button {
  padding: 4px 8px;
  border-radius: 100px;
  border: 0.5px solid rgba(255, 255, 255, 0.7);
  background: rgba(31, 0, 56, 0.4);
  border: none;
  color: #fff;
  font-size: 12px;
  font-weight: 500;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #9d71f7, #8b5cf6);
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .button-text {
    user-select: none;
  }
}

/* 主题变体 */
.theme-map {
  .days-display {
    border-color: #22c55e;

    .get-along-text,
    .days-unit,
    .days-number {
      color: #22c55e;
    }
  }

  .next-day-button {
    background: linear-gradient(135deg, #22c55e, #16a34a);

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #4ade80, #22c55e);
    }
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .day-counter {
    gap: 12px;
  }

  .days-display {
    .get-along-text,
    .days-unit {
      font-size: 12px;
    }

    .days-number {
      font-size: 18px;
    }
  }

  .next-day-button {
    font-size: 12px;
    padding: 6px 12px;
  }
}
</style>

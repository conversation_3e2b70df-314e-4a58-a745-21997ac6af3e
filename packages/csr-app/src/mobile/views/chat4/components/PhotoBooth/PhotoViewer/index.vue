<template>
  <div v-if="visible" class="photo-viewer-overlay" @click="handleOverlayClick">
    <div class="photo-viewer-container">
      <!-- 照片显示区域 -->
      <div class="photo-display" @click.stop>
        <img :src="photoUrl" :alt="photoAlt" class="photo-image" />
      </div>

      <!-- 底部操作栏 -->
      <div class="action-bar" @click.stop>
        <!-- 返回按钮 -->
        <button class="action-btn back-btn" @click="handleBack">
          <div class="btn-icon">
            <BackArrowIcon class="action-icon" />
          </div>
        </button>

        <!-- 下载按钮 -->
        <button class="action-btn download-btn" @click.prevent="handleDownload">
          <div class="btn-icon">
            <DownloadIcon class="action-icon" />
          </div>
        </button>

        <!-- 分享按钮 -->
        <button class="action-btn share-btn" @click.prevent="handleShare">
          <div class="btn-icon">
            <ShareIcon class="action-icon" />
          </div>
          <!-- 钻石奖励显示 -->
          <div class="diamond-badge">
            <DiamondIcon class="diamond-icon" />
            <span class="diamond-text">+20</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BackArrowIcon from '@/assets/icon/photo-viewer-back-arrow.svg'
import DownloadIcon from '@/assets/icon/photo-viewer-download-icon.svg'
import ShareIcon from '@/assets/icon/photo-viewer-share-icon.svg'
import DiamondIcon from '@/assets/icon/diamond-icon.svg'

// Props
interface Props {
  visible: boolean
  photoUrl: string
  photoAlt?: string
  likeCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  photoAlt: '照片',
  likeCount: 0,
})

// Emits
const emit = defineEmits<{
  close: []
  download: [url: string]
  share: [url: string]
}>()

// 处理返回
const handleBack = () => {
  emit('close')
}

// 处理点击遮罩层关闭
const handleOverlayClick = () => {
  emit('close')
}

// 处理下载
const handleDownload = async (event?: Event) => {
  // 阻止默认行为和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  try {
    // 使用fetch获取图片数据
    const response = await fetch(props.photoUrl)
    if (!response.ok) {
      throw new Error('Failed to fetch image')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `photo-booth-${Date.now()}.jpg`
    document.body.appendChild(link)
    link.click()

    // 清理
    window.URL.revokeObjectURL(url)
    document.body.removeChild(link)

    // 仍然emit事件，以便父组件知道下载完成
    emit('download', props.photoUrl)
  } catch (error) {
    console.error('Download failed:', error)
    // 降级方案：尝试直接打开图片
    window.open(props.photoUrl, '_blank')
  }
}

// 处理分享
const handleShare = async (event?: Event) => {
  // 阻止默认行为和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  try {
    // 检查是否支持原生分享
    if (navigator.share) {
      // 对于图片分享，我们需要获取blob数据
      const response = await fetch(props.photoUrl)
      const blob = await response.blob()
      const file = new File([blob], `photo-booth-${Date.now()}.jpg`, {
        type: blob.type,
      })

      await navigator.share({
        title: 'Photo Booth照片',
        text: '看看我拍的照片！',
        files: [file],
      })
    } else {
      // 降级方案：复制链接到剪贴板
      await navigator.clipboard.writeText(props.photoUrl)
      // 这里可以显示一个提示消息
      console.log('图片链接已复制到剪贴板')
    }

    // emit事件通知父组件
    emit('share', props.photoUrl)
  } catch (error) {
    console.error('Share failed:', error)
    // 最终降级方案：emit事件让父组件处理
    emit('share', props.photoUrl)
  }
}
</script>

<style lang="less" scoped>
.photo-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.photo-viewer-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.photo-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;

  .photo-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    animation: photoZoomIn 0.4s ease-out;
  }
}

@keyframes photoZoomIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.action-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 40px 40px;
  gap: 40px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );

  .action-btn {
    position: relative;
    width: 56px;
    height: 56px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }

    .btn-icon {
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .diamond-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      background: linear-gradient(135deg, #1f0038 0%, #2d0052 100%);
      border: 1px solid #daff96;
      border-radius: 20px;
      padding: 1px 6px;
      display: flex;
      align-items: center;
      gap: 2px;
      min-width: 32px;
      height: 16px;

      .diamond-icon {
        width: 12px;
        height: 12px;
      }

      .diamond-text {
        color: #daff96;
        font-size: 9px;
        font-weight: 600;
        line-height: 1;
      }
    }
  }

  .download-btn {
    .action-icon {
      transform: translateY(1px);
    }
  }

  .share-btn {
    .action-icon {
      transform: translateY(-1px);
    }
  }
}

// 响应式适配
@media (max-width: 480px) {
  .action-bar {
    padding: 15px 20px 30px;
    gap: 30px;

    .action-btn {
      width: 48px;
      height: 48px;
    }
  }

  .photo-display {
    padding: 15px;
  }
}
</style>

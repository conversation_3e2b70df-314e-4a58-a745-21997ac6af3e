<template>
  <div v-if="visible" class="photo-album-modal">
    <!-- 相册内容 -->
    <div class="album-container">
      <!-- 标题栏 -->
      <div class="album-header">
        <button class="close-btn" @click="handleClose">
          <ArrowLeftIcon />
        </button>
        <h1 class="album-title">Photo Album</h1>
        <div class="header-spacer"></div>
      </div>

      <!-- 相册内容区域 -->
      <div class="album-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p class="loading-text">Loading photos...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-state">
          <div class="error-icon">📷</div>
          <p class="error-text">{{ error }}</p>
          <button class="retry-btn" @click="fetchPhotos">Try Again</button>
        </div>

        <!-- 空状态 -->
        <div v-else-if="photos.length === 0" class="empty-state">
          <div class="empty-icon">📸</div>
          <p class="empty-text">No photos yet</p>
          <p class="empty-subtitle">Take some photos to see them here!</p>
        </div>

        <!-- 照片网格 -->
        <div v-else class="photos-grid">
          <div
            v-for="(photo, index) in photos"
            :key="index"
            class="photo-item"
            @click="openPhotoViewer(photo, index)"
          >
            <img :src="photo" :alt="`Photo ${index + 1}`" />
            <div class="photo-overlay">
              <div class="view-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z"
                    fill="currentColor"
                  />
                  <path
                    d="M12.0003 5C7.52443 5 3.73132 7.94288 2.45723 12C3.73132 16.0571 7.52443 19 12.0003 19C16.4762 19 20.2693 16.0571 20.5434 12C20.2693 7.94288 16.4762 5 12.0003 5Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 照片查看器 -->
    <PhotoViewer
      :visible="showPhotoViewer"
      :photo-url="selectedPhoto"
      :photo-alt="`Album Photo ${selectedPhotoIndex + 1}`"
      :like-count="0"
      @close="closePhotoViewer"
      @download="handlePhotoDownload"
      @share="handlePhotoShare"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useStoryStore } from '@/store/story'
import axios from 'axios'
import PhotoViewer from '../PhotoViewer/index.vue'
import ArrowLeftIcon from '@/assets/icon/arrow-left.svg'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// Store
const storyStore = useStoryStore()

// 状态
const loading = ref(false)
const error = ref('')
const photos = ref<string[]>([])

// 照片查看器状态
const showPhotoViewer = ref(false)
const selectedPhoto = ref('')
const selectedPhotoIndex = ref(0)

// 获取相册照片
const fetchPhotos = async () => {
  if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) {
    error.value = 'Story or actor information is missing'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const response = await axios.post('/api/v1/photo-stickers.album', {
      story_id: storyStore.currentStory.id,
      actor_id: storyStore.currentActor.id,
    })

    if (response.data.code === '0') {
      photos.value = response.data.data.source || []
    } else {
      error.value = response.data.message || 'Failed to load photos'
    }
  } catch (err) {
    console.error('Failed to fetch photo album:', err)
    error.value = 'Network error, please try again'
  } finally {
    loading.value = false
  }
}

// 监听visible变化，自动获取数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      fetchPhotos()
    } else {
      // 关闭时重置状态
      photos.value = []
      error.value = ''
      closePhotoViewer()
    }
  },
)

// 事件处理
const handleClose = () => {
  emit('close')
}

const openPhotoViewer = (photo: string, index: number) => {
  selectedPhoto.value = photo
  selectedPhotoIndex.value = index
  showPhotoViewer.value = true
}

const closePhotoViewer = () => {
  showPhotoViewer.value = false
  selectedPhoto.value = ''
  selectedPhotoIndex.value = 0
}

const handlePhotoDownload = () => {
  if (selectedPhoto.value) {
    const link = document.createElement('a')
    link.href = selectedPhoto.value
    link.download = `photo-${selectedPhotoIndex.value + 1}.jpg`
    link.click()
  }
}

const handlePhotoShare = () => {
  if (navigator.share && selectedPhoto.value) {
    navigator
      .share({
        title: 'Photo from Album',
        url: selectedPhoto.value,
      })
      .catch(console.error)
  }
}
</script>

<style lang="less" scoped>
.photo-album-modal {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #1a1a1a;
  animation: slideInFromRight 0.3s ease-out;
}

.album-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 标题栏
.album-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  flex-shrink: 0;
  min-height: 64px;

  .close-btn {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;

    svg {
      width: 24px;
      height: 24px;
      fill: white;
    }
  }

  .album-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .header-spacer {
    width: 44px;
  }
}

// 内容区域
.album-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #1a1a1a;
}

// 加载状态
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #333;
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 16px;
    opacity: 0.8;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 错误状态
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;
  text-align: center;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-text {
    font-size: 16px;
    margin-bottom: 16px;
    opacity: 0.8;
  }

  .retry-btn {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background: #0056b3;
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: white;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .empty-subtitle {
    font-size: 14px;
    opacity: 0.6;
  }
}

// 照片网格
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  padding-bottom: 20px;

  // 确保手机端至少3列
  @media (max-width: 480px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  // 平板和PC端可以更多列
  @media (min-width: 481px) {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 16px;
  }
}

.photo-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;

    .view-icon {
      width: 24px;
      height: 24px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  &:hover .photo-overlay {
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>

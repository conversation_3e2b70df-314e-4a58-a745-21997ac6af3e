<template>
  <div v-if="visible" class="album-guide-overlay">
    <div class="album-guide-container">
      <!-- 指向相册按钮的箭头 -->
      <div class="guide-arrow"></div>

      <!-- 引导内容 -->
      <div class="guide-content">
        <div class="guide-text"> Check your album! </div>
        <div class="guide-subtext"> Your photos are saved here </div>
      </div>

      <!-- 关闭按钮 -->
      <button class="guide-close" @click="handleDismiss"> × </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  visible: boolean
  autoHide?: boolean
  autoHideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoHide: true,
  autoHideDelay: 4000,
})

// Emits
const emit = defineEmits<{
  dismiss: []
}>()

let autoHideTimer: number | null = null

// 处理关闭
const handleDismiss = () => {
  emit('dismiss')
}

// 自动隐藏逻辑
onMounted(() => {
  if (props.visible && props.autoHide) {
    autoHideTimer = window.setTimeout(() => {
      handleDismiss()
    }, props.autoHideDelay)
  }
})

onUnmounted(() => {
  if (autoHideTimer) {
    clearTimeout(autoHideTimer)
  }
})
</script>

<style lang="less" scoped>
.album-guide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 25;
  pointer-events: none;
}

.album-guide-container {
  position: absolute;
  bottom: 120px; // 调整到相册按钮上方
  right: 60px; // 调整到相册按钮附近
  pointer-events: auto;
  animation: guideSlideIn 0.4s ease-out;
}

@keyframes guideSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.guide-arrow {
  position: absolute;
  bottom: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.95);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.guide-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 180px;
  position: relative;
}

.guide-text {
  color: #1a1a1a;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 4px;
}

.guide-subtext {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.guide-close {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 响应式适配
@media (max-width: 480px) {
  .album-guide-container {
    bottom: 110px;
    right: 50px;
  }

  .guide-content {
    padding: 14px 18px;
    min-width: 160px;
  }

  .guide-text {
    font-size: 15px;
  }

  .guide-subtext {
    font-size: 13px;
  }
}
</style>

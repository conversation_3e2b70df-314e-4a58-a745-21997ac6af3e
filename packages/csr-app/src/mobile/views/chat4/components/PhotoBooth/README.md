# PhotoBooth 组件集合

这个目录包含了所有与大头贴功能相关的组件。

## 组件结构

```
PhotoBooth/
├── PhotoBoothContainer/     # 主要的大头贴容器组件
├── PhotoViewer/            # 照片查看器组件
├── PhotoGuide/             # 拍照引导组件
├── AlbumGuide/             # 相册引导组件
├── PhotoAlbum/             # 相册组件（新增）
├── index.ts                # 统一导出文件
└── README.md               # 说明文档
```

## 组件说明

### PhotoBoothContainer
主要的大头贴容器组件，包含：
- 相机界面
- 动作选择
- 拍照功能
- 倒计时
- 拍立得效果

### PhotoViewer
照片查看器，用于查看拍摄的照片，支持：
- 全屏查看
- 下载功能
- 分享功能

### PhotoGuide
首次拍照引导组件，帮助用户了解如何使用拍照功能。

### AlbumGuide
相册引导组件，引导用户如何查看相册。

### PhotoAlbum（新增）
相册组件，用于显示用户的照片集合：
- 网格布局显示照片
- 点击照片查看大图
- 支持加载状态和错误处理
- 调用 `/api/v1/photo-stickers.album` API

## 使用方法

```typescript
// 导入单个组件
import { PhotoBoothContainer } from './components/PhotoBooth'

// 或者导入多个组件
import { 
  PhotoBoothContainer, 
  PhotoViewer, 
  PhotoAlbum 
} from './components/PhotoBooth'
```

## API 接口

### 相册接口
- **URL**: `/api/v1/photo-stickers.album`
- **方法**: POST
- **参数**: 
  ```json
  {
    "story_id": "故事ID",
    "actor_id": "角色ID"
  }
  ```
- **响应**:
  ```json
  {
    "code": "0",
    "message": "success",
    "data": {
      "source": ["照片URL数组"]
    }
  }
  ```

## 功能特性

1. **统一的组件管理**: 所有大头贴相关组件集中在一个目录下
2. **模块化设计**: 每个组件职责单一，可独立使用
3. **完整的用户体验**: 从拍照到查看相册的完整流程
4. **响应式设计**: 适配移动端界面
5. **错误处理**: 完善的加载和错误状态处理

## 更新日志

- **v1.0.0**: 初始版本，包含基础拍照功能
- **v1.1.0**: 新增相册功能，重新组织组件结构

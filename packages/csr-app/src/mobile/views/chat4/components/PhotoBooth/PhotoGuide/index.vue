<template>
  <div v-if="visible" class="photo-guide-tooltip">
    <!-- 指向箭头 -->
    <div class="tooltip-arrow"></div>

    <!-- 引导内容 -->
    <div class="tooltip-content" @click="handleDismiss">
      <div class="tooltip-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path
            d="M15 3H6A3 3 0 0 0 3 6V18A3 3 0 0 0 6 21H18A3 3 0 0 0 21 18V9M15 3L21 9M15 3V9H21"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="tooltip-text">Tap to enlarge</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted } from 'vue'

// Props
interface Props {
  visible: boolean
  autoHide?: boolean
  autoHideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoHide: true,
  autoHideDelay: 4000, // 4秒后自动消失
})

// Emits
const emit = defineEmits<{
  dismiss: []
}>()

// 处理关闭引导
const handleDismiss = () => {
  emit('dismiss')
}

// 自动隐藏逻辑
let autoHideTimer: number | null = null

const startAutoHide = () => {
  if (props.autoHide && autoHideTimer === null) {
    autoHideTimer = window.setTimeout(() => {
      handleDismiss()
      autoHideTimer = null
    }, props.autoHideDelay)
  }
}

const clearAutoHide = () => {
  if (autoHideTimer !== null) {
    clearTimeout(autoHideTimer)
    autoHideTimer = null
  }
}

// 监听 visible 变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      startAutoHide()
    } else {
      clearAutoHide()
    }
  },
)

// 组件卸载时清理定时器
onMounted(() => {
  return () => {
    clearAutoHide()
  }
})
</script>

<style lang="less" scoped>
.photo-guide-tooltip {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 25;
  animation: tooltipAppear 0.6s ease-out;
}

@keyframes tooltipAppear {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
  }
  60% {
    opacity: 1;
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tooltip-arrow {
  position: absolute;
  top: -6px;
  left: 24px;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  border-radius: 2px 0 0 0;
}

.tooltip-content {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 12px rgba(34, 197, 94, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  animation: tooltipFloat 3s ease-in-out infinite;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 6px 16px rgba(34, 197, 94, 0.15),
      0 4px 8px rgba(0, 0, 0, 0.08);
    border-color: rgba(34, 197, 94, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

@keyframes tooltipFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

.tooltip-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 6px;
  color: #059669;
  flex-shrink: 0;

  svg {
    width: 14px;
    height: 14px;
  }
}

.tooltip-text {
  font-size: 13px;
  font-weight: 500;
  color: #047857;
  white-space: nowrap;
  letter-spacing: 0.2px;
}

// 响应式适配
@media (max-width: 480px) {
  .photo-guide-tooltip {
    bottom: 15px;
    right: 15px;
  }

  .tooltip-content {
    padding: 10px 14px;
    gap: 6px;
  }

  .tooltip-icon {
    width: 18px;
    height: 18px;

    svg {
      width: 12px;
      height: 12px;
    }
  }

  .tooltip-text {
    font-size: 12px;
  }

  .tooltip-arrow {
    left: 20px;
  }
}
</style>

<template>
  <div class="photo-booth-container">
    <!-- 标题栏 -->
    <div class="header">
      <button class="back-btn" @click="handleBackClick">
        <ArrowLeftIcon />
      </button>
      <h1 class="title">Photo booth</h1>
      <button class="sound-btn">
        <SoundOnIcon />
      </button>
    </div>

    <!-- 主屏幕显示区域 -->
    <div class="screen-container">
      <!-- 拍立得吐照片区域 -->
      <div class="photo-ejection-slot">
        <div class="slot-opening"></div>
        <!-- 吐出的照片 -->
        <div
          v-if="showEjectedPhoto"
          class="ejected-photo"
          :class="{ ejecting: isEjecting }"
        >
          <div class="ejected-polaroid">
            <img
              src="@/assets/icon/polaroid-paper-bg.png"
              alt="拍立得相纸"
              class="ejected-polaroid-bg"
            />
            <div class="ejected-polaroid-frame" @click="openPhotoViewer">
              <img :src="capturedPhoto" alt="拍摄照片" class="ejected-image" />
            </div>
            <!-- 关闭按钮 -->
            <div class="ejected-close-button" @click="closeEjectedPhoto"></div>
          </div>
        </div>
      </div>

      <div class="screen">
        <!-- 屏幕内容 -->
        <div class="screen-content">
          <!-- 人物插画 -->
          <div class="character-illustration">
            <img :src="currentActionImage" alt="人物插画" />
          </div>

          <!-- 屏幕四角白色圆角装饰 -->
          <div class="screen-corners">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>

          <!-- 屏幕状态指示器 -->
          <div class="screen-indicators">
            <!-- 左上角 REC -->
            <div class="indicator top-left">
              <div class="rec-dot"></div>
              <span>REC</span>
            </div>

            <!-- 右上角 HD -->
            <div class="indicator top-right">
              <span>HD</span>
            </div>

            <!-- 左下角 8K -->
            <div class="indicator bottom-left">
              <span>8K</span>
            </div>

            <!-- 右下角 日期 -->
            <div class="indicator bottom-right">
              <span>{{ currentDate }}</span>
            </div>
          </div>

          <!-- 倒计时显示 -->
          <div v-if="captureState === 'countdown'" class="countdown-overlay">
            <div class="countdown-number">{{ countdownNumber }}</div>
          </div>

          <!-- 处理中状态 -->
          <div v-if="captureState === 'processing'" class="processing-overlay">
            <div class="processing-spinner"></div>
            <div class="processing-text">Processing...</div>
          </div>

          <!-- 拍立得相纸 -->
          <div v-if="showPolaroid" class="polaroid-container">
            <div class="polaroid-paper">
              <!-- 拍立得背景 -->
              <img
                src="@/assets/icon/polaroid-paper-bg.png"
                alt="拍立得相纸"
                class="polaroid-bg"
              />

              <!-- 白色相框区域 -->
              <div class="polaroid-frame" @click="openPhotoViewer">
                <!-- 拍摄的照片 -->
                <img
                  :src="capturedPhoto"
                  alt="拍摄照片"
                  class="captured-image"
                />
              </div>

              <!-- 关闭按钮 -->
              <div class="close-button" @click="closePolaroid"></div>
            </div>
          </div>

          <!-- 首次拍照引导 -->
          <PhotoGuide
            :visible="showFirstTimeGuide"
            :auto-hide="true"
            :auto-hide-delay="5000"
            @dismiss="dismissGuide"
          />

          <!-- 相册引导 -->
          <AlbumGuide
            :visible="showAlbumGuide"
            :auto-hide="true"
            :auto-hide-delay="4000"
            @dismiss="dismissAlbumGuide"
          />
        </div>
      </div>
    </div>

    <!-- 动作选择区域 - 基于Figma设计 -->
    <div class="action-selector">
      <div class="action-selector-wrapper">
        <!-- 左侧导航按钮 -->
        <button
          class="nav-button left"
          :disabled="currentActionIndex <= 0"
          @click="previousAction"
        >
          <PhotoBoothLeftArrow />
        </button>

        <!-- 动作容器 -->
        <div class="action-container">
          <!-- 中层容器 -->
          <div class="action-inner">
            <!-- 动作库框架 -->
            <div class="action-frame">
              <!-- 可滚动的动作列表 -->
              <div
                ref="actionListRef"
                class="action-list"
                @scroll="handleScroll"
                @touchstart="handleTouchStart"
                @touchmove="handleTouchMove"
                @touchend="handleTouchEnd"
              >
                <div
                  v-for="(action, index) in actions"
                  :key="index"
                  class="action-item"
                  :class="{
                    selected: currentActionIndex === index,
                    disabled: isInteractionDisabled,
                  }"
                  @click="!isInteractionDisabled && selectAction(index)"
                >
                  <div class="action-image-container">
                    <img :src="action.image" :alt="`Action ${index + 1}`" />
                    <div
                      v-if="currentActionIndex !== index"
                      class="action-overlay"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧导航按钮 -->
        <button
          class="nav-button right"
          :disabled="currentActionIndex >= actions.length - 1"
          @click="nextAction"
        >
          <PhotoBoothRightArrow />
        </button>
      </div>
    </div>

    <!-- 底部控制按钮 -->
    <div class="bottom-controls">
      <!-- 背景 -->
      <div class="bottom-background">
        <img
          src="https://static.reelplay.ai/static/images/chat/album-bottom-background.webp"
          alt="底部背景"
        />
      </div>

      <!-- 按钮容器 -->
      <div class="buttons-container">
        <!-- 左侧留空 -->
        <div class="button-slot left-slot"></div>

        <!-- 中间拍照按钮 -->
        <button
          class="control-btn capture-btn"
          :class="{ disabled: isInteractionDisabled }"
          :disabled="isInteractionDisabled"
          @click="handleCaptureClick"
        >
          <div class="btn-content">
            <img
              src="https://static.reelplay.ai/static/images/chat/shutter-button.webp"
              alt="拍照"
            />
          </div>
        </button>

        <!-- 右侧相册按钮 -->
        <button class="control-btn album-btn" @click="handleAlbumClick">
          <div class="btn-content">
            <AlbumButton />
          </div>
        </button>
      </div>
    </div>

    <!-- 照片查看器 -->
    <PhotoViewer
      :visible="showPhotoViewer"
      :photo-url="capturedPhoto"
      photo-alt="Photo Booth照片"
      :like-count="20"
      @close="closePhotoViewer"
      @download="handlePhotoDownload"
      @share="handlePhotoShare"
    />

    <!-- 相册 -->
    <PhotoAlbum :visible="showPhotoAlbum" @close="closePhotoAlbum" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import PhotoBoothLeftArrow from '@/assets/icon/photo-booth-left-arrow.svg'
import PhotoBoothRightArrow from '@/assets/icon/photo-booth-right-arrow.svg'
import AlbumButton from '@/assets/icon/album-button.svg'
import ArrowLeftIcon from '@/assets/icon/arrow-left.svg'
import SoundOnIcon from '@/assets/icon/sound-on.svg'
import PhotoViewer from '../PhotoViewer/index.vue'
import PhotoGuide from '../PhotoGuide/index.vue'
import AlbumGuide from '../AlbumGuide/index.vue'
import PhotoAlbum from '../PhotoAlbum/index.vue'

// Props
interface Props {
  characterName?: string
  characterAvatar?: string
}

defineProps<Props>()

// 直接使用store
const chatEventsStore = useChatEventsStore()

// Emits
const emit = defineEmits<{
  backClick: []
  albumClick: []
  captureClick: []
}>()

// 默认占位图片（当没有服务器资源时使用）
const defaultActionImage =
  'https://via.placeholder.com/400x400/f0f0f0/999?text=Action'

// 动作数据 - 支持从resource_group获取或使用默认图片
interface ActionItem {
  image: string
  selected: boolean
  tag?: string
}

// 初始化为空数组，等待服务器资源组数据
// 如果没有服务器数据，将显示默认占位图片
const actions = ref<ActionItem[]>([
  { image: defaultActionImage, selected: true },
])

// 资源组数据
const resourceGroups = ref<Array<{ tag: string; type: string; url: string }>>(
  [],
)

// 更新动作数据的函数
const updateActionsFromResourceGroups = (
  groups: Array<{ tag: string; type: string; url: string }>,
) => {
  resourceGroups.value = groups

  // 使用资源组的图片更新动作数据，只显示服务器返回的图片
  const newActions: ActionItem[] = groups.map((group, index) => ({
    image: group.url,
    selected: index === 0,
    tag: group.tag,
  }))

  actions.value = newActions
  currentActionIndex.value = 0
}

// 当前选中的动作索引
const currentActionIndex = ref(0)

// 当前动作图片
const currentActionImage = computed(() => {
  return actions.value[currentActionIndex.value]?.image || defaultActionImage
})

// 是否应该禁用交互（拍照按钮和动作选择）
const isInteractionDisabled = computed(() => {
  return (
    captureState.value === 'countdown' ||
    captureState.value === 'capturing' ||
    captureState.value === 'processing'
  )
})

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  const day = String(now.getDate()).padStart(2, '0')
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const year = now.getFullYear()
  return `${day}/${month}/${year}`
})

// 拍照状态管理
type CaptureState =
  | 'idle'
  | 'countdown'
  | 'capturing'
  | 'processing'
  | 'showing'
const captureState = ref<CaptureState>('idle')
const countdownNumber = ref(3)
const capturedPhoto = ref('')
const showPolaroid = ref(false)

// 倒计时控制
let countdownTimer: number | null = null
let countdownCancelled = false

// 倒计时期间收到的图片暂存
let pendingPhotoUrl = ''

// 照片查看器状态
const showPhotoViewer = ref(false)

// 相册状态
const showPhotoAlbum = ref(false)

// 吐照片动画状态
const showEjectedPhoto = ref(false)
const isEjecting = ref(false)

// 首次拍照引导状态
const showFirstTimeGuide = ref(false)
const hasShownGuide = ref(false)

// 相册引导状态
const showAlbumGuide = ref(false)
const hasShownAlbumGuide = ref(false)

// 检查是否已经显示过引导
const checkGuideStatus = () => {
  const guideSeen = localStorage.getItem('photo-booth-guide-seen')
  if (guideSeen === 'true') {
    hasShownGuide.value = true
  }

  const albumGuideSeen = localStorage.getItem('photo-booth-album-guide-seen')
  if (albumGuideSeen === 'true') {
    hasShownAlbumGuide.value = true
  }
}

// 方法
const handleBackClick = () => {
  emit('backClick')
}

const handleAlbumClick = () => {
  showPhotoAlbum.value = true
  emit('albumClick')
}

// 处理接收到的照片
const handlePhotoReceived = (photoUrl: string) => {
  // 设置拍摄的照片
  capturedPhoto.value = photoUrl

  // 开始吐照片动画
  startPhotoEjection()

  captureState.value = 'showing'

  // 重置倒计时状态，但保持拍立得显示
  setTimeout(() => {
    captureState.value = 'idle'
    countdownNumber.value = 3
    // 不隐藏拍立得相纸，让它保持显示

    // 显示首次拍照引导（如果是第一次拍照）
    if (!hasShownGuide.value) {
      setTimeout(() => {
        showFirstTimeGuide.value = true
        hasShownGuide.value = true
        localStorage.setItem('photo-booth-guide-seen', 'true')
      }, 1500) // 拍立得完全显示后1.5秒显示引导
    }
  }, 1000) // 1秒后重置状态
}

// 开始吐照片动画
const startPhotoEjection = () => {
  // 显示吐出的照片
  showEjectedPhoto.value = true
  isEjecting.value = true

  // 吐照片动画完成后，停止动画但保持照片显示
  setTimeout(() => {
    isEjecting.value = false
    // 不显示屏幕内的拍立得，只保留吐出的大照片
    // showPolaroid.value = true  // 注释掉这行

    // 不隐藏吐出的照片，让它保持显示
    // setTimeout(() => {
    //   showEjectedPhoto.value = false
    // }, 1000)
  }, 2000) // 2秒吐照片动画
}

const closePolaroid = () => {
  showPolaroid.value = false
  capturedPhoto.value = ''
  captureState.value = 'idle'
}

// 关闭吐出的照片
const closeEjectedPhoto = () => {
  showEjectedPhoto.value = false
  capturedPhoto.value = ''
  captureState.value = 'idle'
}

// 照片查看器相关方法
const openPhotoViewer = () => {
  if (capturedPhoto.value) {
    showPhotoViewer.value = true
  }
}

const closePhotoViewer = () => {
  showPhotoViewer.value = false
}

// 相册相关方法
const closePhotoAlbum = () => {
  showPhotoAlbum.value = false
}

const handlePhotoDownload = (url: string) => {
  // PhotoViewer组件已经处理了下载逻辑，这里只需要记录或其他业务逻辑
  console.log('Photo download completed:', url)
}

const handlePhotoShare = async (url: string) => {
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'Photo Booth照片',
        text: '看看我拍的照片！',
        url: url,
      })
    } catch (error) {
      console.log('分享取消或失败:', error)
    }
  } else {
    // 降级方案：复制到剪贴板
    try {
      await navigator.clipboard.writeText(url)
      // 这里可以显示一个提示消息
      console.log('链接已复制到剪贴板')
    } catch (error) {
      console.log('复制失败:', error)
    }
  }
}

// 关闭首次引导
const dismissGuide = () => {
  showFirstTimeGuide.value = false
  // 保存到本地存储，避免重复显示
  localStorage.setItem('photo-booth-guide-seen', 'true')
}

// 关闭相册引导
const dismissAlbumGuide = () => {
  showAlbumGuide.value = false
  // 保存到本地存储，避免重复显示
  localStorage.setItem('photo-booth-album-guide-seen', 'true')
}

// 取消倒计时
const cancelCountdown = () => {
  if (countdownTimer) {
    clearTimeout(countdownTimer)
    countdownTimer = null
  }
  countdownCancelled = true
  captureState.value = 'idle'
  countdownNumber.value = 3
  pendingPhotoUrl = '' // 清空暂存的图片
}

// 开始可中断的倒计时
const startCountdown = (): Promise<void> => {
  return new Promise((resolve) => {
    let count = 3

    const countdown = () => {
      if (countdownCancelled) {
        resolve()
        return
      }

      countdownNumber.value = count

      if (count <= 0) {
        resolve()
        return
      }

      count--
      countdownTimer = window.setTimeout(countdown, 1000)
    }

    countdown()
  })
}

const handleCaptureClick = async () => {
  // 如果已经有拍立得显示，重置状态重新拍照
  if (showPolaroid.value) {
    showPolaroid.value = false
    capturedPhoto.value = ''
    captureState.value = 'idle'
    return
  }

  // 如果正在倒计时或拍摄中，取消当前操作重新开始
  if (
    captureState.value === 'countdown' ||
    captureState.value === 'capturing'
  ) {
    cancelCountdown()
    // 短暂延迟后重新开始，让用户看到取消效果
    setTimeout(() => {
      if (captureState.value === 'idle') {
        handleCaptureClick()
      }
    }, 200)
    return
  }

  if (captureState.value !== 'idle') return

  // 重置取消标志
  countdownCancelled = false

  // 开始倒计时
  captureState.value = 'countdown'

  // 在倒计时开始时就发送请求到服务器
  try {
    const currentAction = actions.value[currentActionIndex.value]
    const imageTag = currentAction?.tag

    // 构造option_id，如果有image_tag则包含它
    const optionId = imageTag ? `photo_stickers_${imageTag}` : 'photo_stickers'

    // 立即发送photo_stickers消息到服务器（不等待响应）
    chatEventsStore.sendMessage(
      'photo_stickers', // content
      optionId, // option_id with image_tag
      'text', // msgType
      true, // isTelepathyComplete
      null, // sceneId
      800, // delay
      false, // isJump
      imageTag ? { image_tag: imageTag } : {}, // extraData
    )
  } catch (error) {
    console.error(
      'Failed to send photo stickers message during countdown:',
      error,
    )
    // 如果发送失败，重置状态
    captureState.value = 'idle'
    countdownNumber.value = 3
    return
  }

  // 可中断的倒计时
  await startCountdown()

  // 检查是否被取消
  if (countdownCancelled || captureState.value !== 'countdown') {
    return
  }

  // 拍照完成，检查是否有倒计时期间收到的图片
  if (pendingPhotoUrl) {
    // 如果有暂存的图片，直接使用
    const photoUrl = pendingPhotoUrl
    pendingPhotoUrl = '' // 清空暂存
    handlePhotoReceived(photoUrl)
  } else {
    // 没有暂存图片，设置为处理中状态等待服务器响应
    captureState.value = 'processing'

    // 设置超时机制，如果5秒内没有收到服务器响应，使用默认图片
    setTimeout(() => {
      if (captureState.value === 'processing') {
        handlePhotoReceived(currentActionImage.value)
      }
    }, 5000)
  }

  emit('captureClick')
}

// 动作列表引用
const actionListRef = ref<HTMLElement>()

// 滚动状态（保留用于滚动检测）
const canScrollLeft = ref(false)
const canScrollRight = ref(true)

// 触摸状态
const touchStartX = ref(0)
const touchStartTime = ref(0)
const isDragging = ref(false)

const selectAction = (index: number) => {
  // 如果正在处理中，禁止切换动作
  if (isInteractionDisabled.value) {
    return
  }

  // 重置所有动作的选中状态
  actions.value.forEach((action, i) => {
    action.selected = i === index
  })
  currentActionIndex.value = index

  // 切换动作时取消倒计时
  if (
    captureState.value === 'countdown' ||
    captureState.value === 'capturing'
  ) {
    cancelCountdown()
  }

  // 切换动作时清除拍立得照片
  if (showPolaroid.value) {
    showPolaroid.value = false
    capturedPhoto.value = ''
    captureState.value = 'idle'

    // 如果是第一次清除拍立得，显示相册引导
    if (!hasShownAlbumGuide.value) {
      setTimeout(() => {
        showAlbumGuide.value = true
        hasShownAlbumGuide.value = true
      }, 500) // 延迟显示，让用户看到拍立得消失
    }
  }

  // 滚动到选中的动作
  scrollToAction(index)
}

const scrollToAction = (index: number) => {
  if (!actionListRef.value) return

  const actionItem = actionListRef.value.children[index] as HTMLElement
  if (actionItem) {
    const containerWidth = actionListRef.value.clientWidth
    const itemWidth = actionItem.offsetWidth
    const itemLeft = actionItem.offsetLeft
    const scrollLeft = itemLeft - (containerWidth - itemWidth) / 2

    actionListRef.value.scrollTo({
      left: Math.max(0, scrollLeft),
      behavior: 'smooth',
    })
  }
}

// 移除了 scrollLeft 和 scrollRight 函数，因为按钮现在直接切换动作项

const handleScroll = () => {
  if (!actionListRef.value) return

  const scrollLeft = actionListRef.value.scrollLeft
  const maxScroll =
    actionListRef.value.scrollWidth - actionListRef.value.clientWidth

  canScrollLeft.value = scrollLeft > 0
  canScrollRight.value = scrollLeft < maxScroll
}

const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.touches[0].clientX
  touchStartTime.value = Date.now()
  isDragging.value = false
}

const handleTouchMove = (e: TouchEvent) => {
  const deltaX = Math.abs(e.touches[0].clientX - touchStartX.value)
  if (deltaX > 10) {
    isDragging.value = true
  }
}

const handleTouchEnd = (e: TouchEvent) => {
  const deltaX = e.changedTouches[0].clientX - touchStartX.value
  const deltaTime = Date.now() - touchStartTime.value

  // 快速滑动检测
  if (Math.abs(deltaX) > 50 && deltaTime < 300 && !isDragging.value) {
    if (deltaX > 0 && currentActionIndex.value > 0) {
      selectAction(currentActionIndex.value - 1)
    } else if (
      deltaX < 0 &&
      currentActionIndex.value < actions.value.length - 1
    ) {
      selectAction(currentActionIndex.value + 1)
    }
  }

  isDragging.value = false
}

const previousAction = () => {
  if (currentActionIndex.value > 0) {
    selectAction(currentActionIndex.value - 1)
  }
}

const nextAction = () => {
  if (currentActionIndex.value < actions.value.length - 1) {
    selectAction(currentActionIndex.value + 1)
  }
}

// 初始化
onMounted(() => {
  // 初始化滚动状态
  nextTick(() => {
    handleScroll()
  })

  // 检查引导状态
  checkGuideStatus()

  // 监听chatEventsStore中的数据变化
  // 监听资源组数据
  watch(
    () => chatEventsStore.resourceGroups,
    (newResourceGroups) => {
      if (newResourceGroups && Array.isArray(newResourceGroups)) {
        updateActionsFromResourceGroups(newResourceGroups)
      }
    },
    { immediate: true },
  )

  // 监听show_image数据
  watch(
    () => chatEventsStore.currentImage,
    (newImageUrl) => {
      if (!newImageUrl) return

      // 如果正在倒计时，暂存图片等倒计时完成后处理
      if (captureState.value === 'countdown') {
        pendingPhotoUrl = newImageUrl
        return
      }

      // 如果正在处理中，直接处理图片
      if (
        captureState.value === 'capturing' ||
        captureState.value === 'processing'
      ) {
        handlePhotoReceived(newImageUrl)
      }
    },
    { immediate: false },
  )
})

// 暴露方法给父组件
defineExpose({
  updateActionsFromResourceGroups,
})
</script>

<style lang="less" scoped>
.photo-booth-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 标题栏样式
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #1a1a1a;
  border-bottom: 1px solid #333;

  .back-btn,
  .sound-btn {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;

    svg {
      width: 24px;
      height: 24px;
      fill: white;
    }
  }

  .title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }
}

// 主屏幕容器
.screen-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px; // 减少padding给屏幕更多空间
  // 限制最大高度，确保底部控制区域始终可见
  // 计算：100vh - 头部(~68px) - 底部控制(130px) - 边距(32px) = ~230px
  max-height: calc(100vh - 230px);
  min-height: 300px; // 设置最小高度确保基本可用性
}

// 屏幕样式
.screen {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border-radius: 24px;
  border: 12px solid #333;
  position: relative;
  box-shadow:
    0 0 0 3px #555,
    0 0 0 6px #222,
    0 15px 40px rgba(0, 0, 0, 0.7),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);

  // 限制屏幕的最大尺寸，保持3:4宽高比匹配照片比例
  width: 100%;
  max-width: 350px; // 稍微减小最大宽度，给更多垂直空间
  aspect-ratio: 3/4; // 3:4宽高比，完全匹配照片比例
  max-height: 100%;
}

.screen-content {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
  border-radius: 12px;
}

.character-illustration {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover; // 使用cover，因为屏幕比例已匹配照片3:4比例
    filter: contrast(1.1) brightness(1.05);
  }
}

// 屏幕四角白色圆角装饰
.screen-corners {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;

  .corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.8);

    &.top-left {
      top: 8px;
      left: 8px;
      border-right: none;
      border-bottom: none;
      border-radius: 12px 0 0 0;
    }

    &.top-right {
      top: 8px;
      right: 8px;
      border-left: none;
      border-bottom: none;
      border-radius: 0 12px 0 0;
    }

    &.bottom-left {
      bottom: 8px;
      left: 8px;
      border-right: none;
      border-top: none;
      border-radius: 0 0 0 12px;
    }

    &.bottom-right {
      bottom: 8px;
      right: 8px;
      border-left: none;
      border-top: none;
      border-radius: 0 0 12px 0;
    }
  }
}

// 屏幕指示器
.screen-indicators {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  border-radius: 12px;

  .indicator {
    position: absolute;
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    z-index: 20;

    &.top-left {
      top: 12px;
      left: 10px;
      // background: rgba(255, 255, 255, 0.9);
      color: #000;
      display: flex;
      align-items: center;
      gap: 4px;
      // border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      padding: 4px 8px;
      // backdrop-filter: blur(4px);

      .rec-dot {
        width: 8px;
        height: 8px;
        background: #ff0000;
        border-radius: 50%;
        animation: pulse 1.5s infinite;
      }

      span {
        color: #333;
        font-weight: 700;
        // font-size: 10px;
      }
    }

    &.top-right {
      top: 15px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: rgba(255, 255, 255, 0.7);
      padding: 4px 8px;
      // border-radius: 4px;
      font-size: 10px;
      backdrop-filter: blur(4px);
    }

    &.bottom-left {
      bottom: 18px;
      left: 18px;
      // background: rgba(255, 140, 0, 0.9);
      min-width: 30px;
      color: #cf8725;
      border: 1px solid #cf8725;
      padding: 3px 6px;
      font-size: 10px;
      font-weight: 700;
      text-align: center;
    }

    &.bottom-right {
      bottom: 18px;
      right: 18px;
      color: #cf8725;
      font-size: 10px;
      font-weight: 600;
      padding: 3px 6px;
      // border: 1px solid #cf8725;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.9);
  }
}

// 动作选择器 - 基于Figma设计
.action-selector {
  padding: 16px 20px; // 减少上下padding节省空间

  .action-selector-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
  }

  .action-container {
    width: 100%;
    height: 80px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2b2b2b 100%);
    border-radius: 16px;
    box-shadow:
      0px 1px 6px 0px rgba(0, 0, 0, 0.7),
      0px 2px 8px 0px rgba(0, 0, 0, 1),
      inset 0px 1px 1px 0px rgba(255, 255, 255, 0.25),
      inset 0px -2px 2px 0px rgba(59, 59, 59, 0.8);
    position: relative;
  }

  .action-inner {
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    background: linear-gradient(135deg, #414141 0%, #262626 100%);
    border-radius: 12px;
    box-shadow:
      0px 1px 1px 1px rgba(0, 0, 0, 0.2),
      inset 0px 1px 1px 0px rgba(208, 208, 208, 0.25);
  }

  .action-frame {
    position: absolute;
    top: 6px;
    left: 3px;
    right: 3px;
    bottom: 6px;
    background: linear-gradient(135deg, #494949 0%, #262626 100%);
    border-radius: 11.5px;
    display: flex;
    align-items: center;
    padding: 0 5px;
  }

  .nav-button {
    width: 32px;
    height: 48px;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
    flex-shrink: 0;
    transform-style: preserve-3d;

    &:hover:not(:disabled) {
      transform: scale(1.05);
    }

    &:active:not(:disabled) {
      transform: scale(0.95) translateY(2px);
      filter: brightness(0.9);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    svg {
      width: 30px;
      height: 46px;
      transition: all 0.15s ease;
    }
  }

  .action-list {
    flex: 1;
    display: flex;
    gap: 8px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
    padding: 6px 0;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .action-item {
    width: 54px;
    height: 54px;
    flex-shrink: 0;
    cursor: pointer;
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    &.selected .action-image-container {
      border-color: #ffe91e;
      box-shadow: 0 0 8px rgba(255, 233, 30, 0.5);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;

      &:active {
        transform: none;
      }
    }
  }

  .action-image-container {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .action-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      transition: opacity 0.3s;
    }
  }
}

// 底部控制按钮
.bottom-controls {
  position: relative;
  height: 130px; // 减少高度给屏幕区域更多空间

  .bottom-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .buttons-container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 40px 30px; // 减少padding
    height: 100%;
    box-shadow:
      0 -1px 9px rgba(150, 150, 150, 0.3),
      0 -2px 6px rgba(150, 150, 150, 0.2),
      0 -1px 3px rgba(150, 150, 150, 0.15);
  }

  .button-slot {
    width: 48px;
    height: 48px;
    // 左侧留空，保持布局平衡
  }

  .control-btn {
    border: none;
    cursor: pointer;
    transition: all 0.15s ease;
    transform-style: preserve-3d;
    background: none;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95) translateY(3px);
      filter: brightness(0.9);
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.15s ease;
    }
  }

  .capture-btn {
    width: 110px;
    height: 110px;

    img {
      width: 110px;
      height: 110px;
      object-fit: contain;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .album-btn {
    width: 48px;
    height: 48px;

    svg {
      width: 48px;
      height: 48px;
    }
  }
}

// 倒计时样式
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  z-index: 10;

  .countdown-number {
    font-size: 120px;
    font-weight: 900;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    animation: countdownPulse 1s ease-in-out;
  }
}

@keyframes countdownPulse {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 处理中状态样式
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10;

  .processing-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  .processing-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 拍立得相纸样式
.polaroid-container {
  position: absolute;
  top: -100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 15;
  animation: polaroidSlowDrop 3s ease-out forwards;

  .polaroid-paper {
    position: relative;
    width: 200px; // 固定尺寸，覆盖人物插画
    height: 292px; // 固定尺寸，覆盖人物插画

    .polaroid-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .polaroid-frame {
      position: absolute;
      top: 12px; // 按比例调整
      left: 11px; // 按比例调整
      width: 178px; // 按比例调整
      height: 221px; // 按比例调整
      overflow: hidden;
      border-radius: 3px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      &:active {
        transform: scale(0.98);
      }

      .captured-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0;
        animation: imageReveal 2s ease-out 1s forwards;
        transition: all 0.2s ease;
      }
    }

    .close-button {
      position: absolute;
      top: -8px;
      right: -8px;
      width: 24px;
      height: 24px;
      background: #ff4444;
      border: 2px solid #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;

      &:hover {
        background: #ff6666;
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 12px;
        height: 2px;
        background: #fff;
        border-radius: 1px;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }
    }
  }
}

@keyframes polaroidSlowDrop {
  0% {
    top: -100%;
    transform: translateX(-50%) rotate(-2deg);
  }
  30% {
    top: -50%;
    transform: translateX(-50%) rotate(-1deg);
  }
  60% {
    top: 10%;
    transform: translateX(-50%) rotate(1deg);
  }
  80% {
    top: 25%;
    transform: translateX(-50%) rotate(-0.5deg);
  }
  100% {
    top: 30%;
    transform: translateX(-50%) rotate(0deg);
  }
}

@keyframes imageReveal {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 吐照片区域样式
.photo-ejection-slot {
  position: relative;
  width: 100%;
  height: 20px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: flex-end;

  .slot-opening {
    width: 350px; // 增加宽度以匹配screen的最大宽度
    height: 8px;
    background: linear-gradient(135deg, #333 0%, #1a1a1a 100%);
    border-radius: 4px;
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.5),
      0 1px 2px rgba(255, 255, 255, 0.1);
    border: 1px solid #444;
  }
}

.ejected-photo {
  position: absolute;
  top: 8px; // 从slot-opening的底部开始
  left: 50%;
  transform: translateX(-50%);
  z-index: 25; // 提高z-index，确保覆盖screen的角标
  overflow: hidden; // 关键：隐藏超出部分，实现慢慢挤出的效果

  &.ejecting {
    animation: photoEjection 4s ease-out forwards; // 延长动画时间，更真实
  }

  .ejected-polaroid {
    position: relative;
    width: 350px; // 增加到与screen相同的最大宽度
    height: 467px; // 按3:4比例计算的高度 (350 * 4/3 ≈ 467)
    // 确保照片从底部开始显示（像真实拍立得一样从底部挤出）
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .ejected-polaroid-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .ejected-polaroid-frame {
      position: absolute;
      top: 20px; // 按比例调整
      left: 18px; // 按比例调整
      width: 314px; // 按比例调整 (350 - 36)
      height: 390px; // 按比例调整 (467 - 77)
      overflow: hidden;
      border-radius: 6px; // 稍微增加圆角

      .ejected-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .ejected-close-button {
      position: absolute;
      top: -12px;
      right: -12px;
      width: 32px;
      height: 32px;
      background: #ff4444;
      border: 3px solid #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transition: all 0.2s ease;
      z-index: 20;

      &:hover {
        background: #ff6666;
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 3px;
        background: #fff;
        border-radius: 1.5px;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }
    }
  }
}

@keyframes photoEjection {
  0% {
    transform: translateX(-50%) translateY(0);
    height: 0; // 开始时高度为0，照片完全隐藏
    opacity: 1;
  }
  15% {
    transform: translateX(-50%) translateY(0);
    height: 50px; // 照片开始从出口挤出
    opacity: 1;
  }
  40% {
    transform: translateX(-50%) translateY(0);
    height: 200px; // 照片继续挤出
    opacity: 1;
  }
  70% {
    transform: translateX(-50%) translateY(0);
    height: 467px; // 照片完全挤出
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(28px); // 最终位置：向下移动到screen位置，完美覆盖
    height: 467px;
    opacity: 1;
  }
}
</style>

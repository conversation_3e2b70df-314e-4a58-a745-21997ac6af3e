<svg xmlns="http://www.w3.org/2000/svg" width="110" height="110" viewBox="0 0 110 110" fill="none">
  <g opacity="0.4" filter="url(#filter0_ddinf_11440_10733)">
    <circle cx="55" cy="49" r="47" fill="url(#paint0_linear_11440_10733)"/>
  </g>
  <circle opacity="0.8" cx="55" cy="49" r="39" fill="url(#paint1_linear_11440_10733)"/>
  <g filter="url(#filter1_ddiii_11440_10733)">
    <circle cx="55" cy="49" r="36" fill="url(#paint2_linear_11440_10733)"/>
    <circle cx="55" cy="49" r="36" fill="url(#paint3_linear_11440_10733)" fill-opacity="0.7"/>
    <circle cx="55" cy="49" r="36.25" stroke="#150027" stroke-width="0.5"/>
  </g>
  <g filter="url(#filter2_di_11440_10733)">
    <circle cx="55" cy="49" r="32" fill="url(#paint4_radial_11440_10733)" fill-opacity="0.7" shape-rendering="crispEdges"/>
  </g>
  <path d="M77.215 40.8348C80.6392 42.7516 85.9925 40.1195 84.4605 36.5068C82.8702 32.7567 80.5636 29.3087 77.6274 26.3726C71.6263 20.3714 63.4869 17 55 17C46.5131 17 38.3737 20.3714 32.3726 26.3726C29.4364 29.3087 27.1298 32.7567 25.5395 36.5068C24.0075 40.1195 29.3608 42.7516 32.785 40.8348C37.9256 37.9571 45.2263 35.4242 55 35.4242C64.7737 35.4242 72.0744 37.9571 77.215 40.8348Z" fill="url(#paint5_radial_11440_10733)" style="mix-blend-mode:plus-lighter"/>
  <g opacity="0.2">
    <path d="M79.0596 53.5406C82.2942 52.2162 86.6796 54.7125 85.6894 58.0646C84.1894 63.1422 81.4388 67.816 77.6274 71.6274C71.6263 77.6286 63.4869 81 55 81C46.5131 81 38.3737 77.6286 32.3726 71.6274C28.5612 67.816 25.8106 63.1422 24.3107 58.0646C23.3204 54.7125 27.7058 52.2162 30.9404 53.5406C36.1213 55.6618 43.9922 57.7273 55 57.7273C66.0078 57.7273 73.8787 55.6619 79.0596 53.5406Z" fill="url(#paint6_radial_11440_10733)" style="mix-blend-mode:plus-lighter"/>
  </g>
  <g opacity="0.8" filter="url(#filter3_f_11440_10733)">
    <circle cx="55" cy="49" r="23" fill="url(#paint7_radial_11440_10733)"/>
  </g>
  <defs>
    <filter id="filter0_ddinf_11440_10733" x="0" y="0" width="110" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feMorphology radius="3" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_11440_10733"/>
      <feOffset dy="6"/>
      <feGaussianBlur stdDeviation="2.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11440_10733"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-1"/>
      <feGaussianBlur stdDeviation="0.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="effect1_dropShadow_11440_10733" result="effect2_dropShadow_11440_10733"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.097193 0 0 0 0 0.087697 0 0 0 0 0.5625 0 0 0 0.4 0"/>
      <feBlend mode="screen" in2="shape" result="effect3_innerShadow_11440_10733"/>
      <feTurbulence type="fractalNoise" baseFrequency="2.5 2.5" stitchTiles="stitch" numOctaves="3" result="noise" seed="9761"/>
      <feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise"/>
      <feComponentTransfer in="alphaNoise" result="coloredNoise1">
        <feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
      </feComponentTransfer>
      <feComposite operator="in" in2="effect3_innerShadow_11440_10733" in="coloredNoise1" result="noise1Clipped"/>
      <feComponentTransfer in="alphaNoise" result="coloredNoise2">
        <feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 "/>
      </feComponentTransfer>
      <feComposite operator="in" in2="effect3_innerShadow_11440_10733" in="coloredNoise2" result="noise2Clipped"/>
      <feFlood flood-color="rgba(38, 38, 38, 0.8)" result="color1Flood"/>
      <feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1"/>
      <feFlood flood-color="rgba(0, 0, 0, 0.8)" result="color2Flood"/>
      <feComposite operator="in" in2="noise2Clipped" in="color2Flood" result="color2"/>
      <feMerge result="effect4_noise_11440_10733">
        <feMergeNode in="effect3_innerShadow_11440_10733"/>
        <feMergeNode in="color1"/>
        <feMergeNode in="color2"/>
      </feMerge>
      <feBlend mode="normal" in="effect4_noise_11440_10733" in2="effect2_dropShadow_11440_10733" result="effect4_noise_11440_10733"/>
      <feGaussianBlur stdDeviation="0.5" result="effect5_foregroundBlur_11440_10733"/>
    </filter>
    <filter id="filter1_ddiii_11440_10733" x="13.5" y="8.5" width="83" height="83" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feMorphology radius="2" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_11440_10733"/>
      <feOffset dy="1"/>
      <feGaussianBlur stdDeviation="1.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.360577 0 0 0 0 0.360577 0 0 0 0 0.360577 0 0 0 0.2 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11440_10733"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dx="-1" dy="1"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.447115 0 0 0 0 0.447115 0 0 0 0 0.447115 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="effect1_dropShadow_11440_10733" result="effect2_dropShadow_11440_10733"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_11440_10733" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect3_innerShadow_11440_10733"/>
      <feOffset dy="-1"/>
      <feGaussianBlur stdDeviation="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.152824 0 0 0 0 0.0981879 0 0 0 0 0.346154 0 0 0 1 0"/>
      <feBlend mode="normal" in2="shape" result="effect3_innerShadow_11440_10733"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="0.75"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.37 0"/>
      <feBlend mode="normal" in2="effect3_innerShadow_11440_10733" result="effect4_innerShadow_11440_10733"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="effect4_innerShadow_11440_10733" result="effect5_innerShadow_11440_10733"/>
    </filter>
    <filter id="filter2_di_11440_10733" x="21" y="14" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-1"/>
      <feGaussianBlur stdDeviation="1"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0926127 0 0 0 0 0.05565 0 0 0 0 0.223214 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11440_10733"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11440_10733" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_11440_10733"/>
    </filter>
    <filter id="filter3_f_11440_10733" x="21" y="15" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="5.5" result="effect1_foregroundBlur_11440_10733"/>
    </filter>
    <linearGradient id="paint0_linear_11440_10733" x1="55" y1="2" x2="55" y2="96" gradientUnits="userSpaceOnUse">
      <stop stop-color="#999999"/>
      <stop offset="1" stop-color="#151515"/>
    </linearGradient>
    <linearGradient id="paint1_linear_11440_10733" x1="85" y1="25" x2="33" y2="79" gradientUnits="userSpaceOnUse">
      <stop stop-color="#060606"/>
      <stop offset="1" stop-color="#1B1B1B"/>
    </linearGradient>
    <linearGradient id="paint2_linear_11440_10733" x1="55" y1="13" x2="55" y2="85" gradientUnits="userSpaceOnUse">
      <stop stop-color="#2E2050"/>
      <stop offset="1" stop-color="#4C3CA4"/>
    </linearGradient>
    <linearGradient id="paint3_linear_11440_10733" x1="55" y1="85" x2="55" y2="13" gradientUnits="userSpaceOnUse">
      <stop stop-color="#D0D5E2" stop-opacity="0"/>
      <stop offset="0.634615" stop-color="#180A1F" stop-opacity="0"/>
      <stop offset="1" stop-color="#DFD0E2" stop-opacity="0.9"/>
    </linearGradient>
    <radialGradient id="paint4_radial_11440_10733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(55 81) rotate(-90) scale(64)">
      <stop stop-color="#D0D5E2" stop-opacity="0"/>
      <stop offset="0.636424" stop-color="#180A1F" stop-opacity="0"/>
      <stop offset="1" stop-color="#DFD0E2" stop-opacity="0.9"/>
    </radialGradient>
    <radialGradient id="paint5_radial_11440_10733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(55 17) rotate(90) scale(16)">
      <stop stop-color="#554C83" stop-opacity="0.63"/>
      <stop offset="1" stop-color="#3B2C73" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="paint6_radial_11440_10733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(55 71.5455) rotate(-90) scale(41.0455)">
      <stop stop-color="#332676"/>
      <stop offset="1" stop-color="#3B2C73" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="paint7_radial_11440_10733" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(55 49) rotate(90) scale(28.5)">
      <stop stop-color="#291A70" stop-opacity="0.15"/>
      <stop offset="1" stop-color="#33255F" stop-opacity="0.8"/>
    </radialGradient>
  </defs>
</svg>
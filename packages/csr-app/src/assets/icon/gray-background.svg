<svg xmlns="http://www.w3.org/2000/svg" width="375" height="148" viewBox="0 0 375 148" fill="none">
  <g opacity="0.5" filter="url(#filter0_in_11440_10725)">
    <rect width="375" height="148" transform="matrix(1 0 0 -1 0 148)" fill="#070707"/>
  </g>
  <defs>
    <filter id="filter0_in_11440_10725" x="0" y="0" width="375" height="150" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="3"/>
      <feGaussianBlur stdDeviation="1"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_11440_10725"/>
      <feTurbulence type="fractalNoise" baseFrequency="0.45454543828964233 0.45454543828964233" stitchTiles="stitch" numOctaves="3" result="noise" seed="9100"/>
      <feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise"/>
      <feComponentTransfer in="alphaNoise" result="coloredNoise1">
        <feFuncA type="discrete" tableValues="1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
      </feComponentTransfer>
      <feComposite operator="in" in2="effect1_innerShadow_11440_10725" in="coloredNoise1" result="noise1Clipped"/>
      <feFlood flood-color="rgba(121, 121, 121, 0.25)" result="color1Flood"/>
      <feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1"/>
      <feMerge result="effect2_noise_11440_10725">
        <feMergeNode in="effect1_innerShadow_11440_10725"/>
        <feMergeNode in="color1"/>
      </feMerge>
    </filter>
  </defs>
</svg>